<?php
class WSL_Logger {
    public static function log_removal($user_id, $username = '', $display_name = '', $email = '', $roles = [], $registered_date = '') {
        $logs = get_option('wsl_removed_users', []);

        // Περιορισμό<PERSON> logs σε 1000 εγγραφές για απόδοση
        if (count($logs) >= 1000) {
            $logs = array_slice($logs, -900); // Κρατάμε τις τελευταίες 900
        }

        // Διασφάλιση ότι έχουμε τουλάχιστον βασικές πληροφορίες
        $username = !empty($username) ? $username : 'Άγνωστος';
        $display_name = !empty($display_name) ? $display_name : 'Μη διαθέσιμο';
        $email = !empty($email) ? $email : 'Μη διαθέσιμο';
        $roles_string = '';

        if (is_array($roles) && !empty($roles)) {
            $roles_string = implode(', ', $roles);
        } elseif (!empty($roles)) {
            $roles_string = $roles;
        } else {
            $roles_string = 'Μη διαθέσιμο';
        }

        $logs[] = [
            'user_id' => $user_id,
            'username' => $username,
            'display_name' => $display_name,
            'email' => $email,
            'roles' => $roles_string,
            'registered_date' => $registered_date,
            'last_login' => '', // Θα είναι πάντα κενό για ανενεργούς χρήστες
            'timestamp' => current_time('mysql'),
            'version' => '1.1' // Για να ξεχωρίζουμε τα νέα logs
        ];
        update_option('wsl_removed_users', $logs);
    }

    public static function get_logs() {
        $logs = get_option('wsl_removed_users', []);
        // Επιστροφή σε αντίστροφη σειρά (νεότερα πρώτα)
        return array_reverse($logs);
    }

    public static function clear_logs() {
        delete_option('wsl_removed_users');
    }

    public static function get_logs_count() {
        $logs = get_option('wsl_removed_users', []);
        return count($logs);
    }
}
