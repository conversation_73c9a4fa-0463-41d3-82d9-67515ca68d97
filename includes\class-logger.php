<?php
class WSL_Logger {
    public static function log_removal($user_id, $username = '', $display_name = '', $email = '', $roles = [], $registered_date = '') {
        $logs = get_option('wsl_removed_users', []);

        // Περιορισμό<PERSON> logs σε 1000 εγγραφές για απόδοση
        if (count($logs) >= 1000) {
            $logs = array_slice($logs, -900); // Κρατάμε τις τελευταίες 900
        }

        // Διασφάλιση ότι έχουμε τουλάχιστον βασικές πληροφορίες
        $username = !empty($username) ? $username : 'Άγνωστος';
        $display_name = !empty($display_name) ? $display_name : 'Μη διαθέσιμο';
        $email = !empty($email) ? $email : 'Μη διαθέσιμο';
        $roles_string = '';

        if (is_array($roles) && !empty($roles)) {
            $roles_string = implode(', ', $roles);
        } elseif (!empty($roles)) {
            $roles_string = $roles;
        } else {
            $roles_string = 'Μη διαθέσιμο';
        }

        $logs[] = [
            'user_id' => $user_id,
            'username' => $username,
            'display_name' => $display_name,
            'email' => $email,
            'roles' => $roles_string,
            'registered_date' => $registered_date,
            'last_login' => '', // Θα είναι πάντα κενό για ανενεργούς χρήστες
            'timestamp' => current_time('mysql'),
            'version' => '1.1' // Για να ξεχωρίζουμε τα νέα logs
        ];
        update_option('wsl_removed_users', $logs);
    }

    public static function get_logs() {
        $logs = get_option('wsl_removed_users', []);
        // Επιστροφή σε αντίστροφη σειρά (νεότερα πρώτα)
        return array_reverse($logs);
    }

    public static function clear_logs() {
        delete_option('wsl_removed_users');
    }

    public static function get_logs_count() {
        $logs = get_option('wsl_removed_users', []);
        return count($logs);
    }

    public static function get_filtered_logs($filters = []) {
        $logs = self::get_logs();

        if (empty($filters)) {
            return $logs;
        }

        $filtered = [];

        foreach ($logs as $log) {
            $include = true;

            // Φίλτρο ημερομηνίας από
            if (!empty($filters['date_from'])) {
                $log_date = date('Y-m-d', strtotime($log['timestamp']));
                if ($log_date < $filters['date_from']) {
                    $include = false;
                }
            }

            // Φίλτρο ημερομηνίας έως
            if (!empty($filters['date_to'])) {
                $log_date = date('Y-m-d', strtotime($log['timestamp']));
                if ($log_date > $filters['date_to']) {
                    $include = false;
                }
            }

            // Φίλτρο username
            if (!empty($filters['username'])) {
                $username = $log['username'] ?? '';
                if (stripos($username, $filters['username']) === false) {
                    $include = false;
                }
            }

            // Φίλτρο email
            if (!empty($filters['email'])) {
                $email = $log['email'] ?? '';
                if (stripos($email, $filters['email']) === false) {
                    $include = false;
                }
            }

            // Φίλτρο ρόλου
            if (!empty($filters['role'])) {
                $roles = $log['roles'] ?? '';
                if (stripos($roles, $filters['role']) === false) {
                    $include = false;
                }
            }

            // Φίλτρο User ID
            if (!empty($filters['user_id'])) {
                if ($log['user_id'] != $filters['user_id']) {
                    $include = false;
                }
            }

            if ($include) {
                $filtered[] = $log;
            }
        }

        return $filtered;
    }

    public static function search_logs($search_term) {
        $logs = self::get_logs();

        if (empty($search_term)) {
            return $logs;
        }

        $filtered = [];
        $search_term = strtolower($search_term);

        foreach ($logs as $log) {
            $searchable_text = strtolower(implode(' ', [
                $log['user_id'],
                $log['username'] ?? '',
                $log['display_name'] ?? '',
                $log['email'] ?? '',
                $log['roles'] ?? '',
                $log['timestamp']
            ]));

            if (strpos($searchable_text, $search_term) !== false) {
                $filtered[] = $log;
            }
        }

        return $filtered;
    }
}
