<?php
class WSL_Settings {
    public static function init() {
        add_action('admin_init', [self::class, 'register_settings']);
    }

    public static function register_settings() {
        register_setting('wsl_settings_group', 'wsl_timeout_minutes', [
            'type' => 'integer',
            'sanitize_callback' => [self::class, 'sanitize_timeout'],
            'default' => 60
        ]);

        register_setting('wsl_settings_group', 'wsl_notify_admin', [
            'type' => 'boolean',
            'sanitize_callback' => [self::class, 'sanitize_boolean'],
            'default' => false
        ]);

        register_setting('wsl_settings_group', 'wsl_exclude_roles', [
            'type' => 'array',
            'sanitize_callback' => [self::class, 'sanitize_roles'],
            'default' => ['administrator']
        ]);

        add_settings_section(
            'wsl_main_section',
            'Κύριες Ρυθμίσεις',
            [self::class, 'main_section_callback'],
            'wsl-settings'
        );

        add_settings_field(
            'wsl_timeout_minutes',
            'Χρονικό Όριο (λεπτά)',
            [self::class, 'timeout_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        add_settings_field(
            'wsl_notify_admin',
            'Ειδοποίηση Διαχειριστή',
            [self::class, 'notify_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        add_settings_field(
            'wsl_exclude_roles',
            'Εξαίρεση Ρόλων',
            [self::class, 'exclude_roles_field'],
            'wsl-settings',
            'wsl_main_section'
        );
    }

    public static function main_section_callback() {
        echo '<p>Ρυθμίστε τις παραμέτρους για την αυτόματη αφαίρεση ανενεργών χρηστών.</p>';
    }

    public static function timeout_field() {
        $value = get_option('wsl_timeout_minutes', 60);
        echo "<input type='number' name='wsl_timeout_minutes' value='" . esc_attr($value) . "' min='1' max='43200' />";
        echo "<p class='description'>Χρόνος σε λεπτά μετά την εγγραφή πριν τη διαγραφή (1-43200 λεπτά / 1 μήνας)</p>";
    }

    public static function notify_field() {
        $value = get_option('wsl_notify_admin', false);
        echo "<input type='checkbox' name='wsl_notify_admin' value='1' " . checked(1, $value, false) . " />";
        echo "<label for='wsl_notify_admin'>Αποστολή email στον διαχειριστή όταν διαγράφονται χρήστες</label>";
    }

    public static function exclude_roles_field() {
        $selected_roles = get_option('wsl_exclude_roles', ['administrator']);
        $all_roles = wp_roles()->get_names();

        echo "<fieldset>";
        foreach ($all_roles as $role_key => $role_name) {
            $checked = in_array($role_key, $selected_roles) ? 'checked' : '';
            echo "<label><input type='checkbox' name='wsl_exclude_roles[]' value='" . esc_attr($role_key) . "' $checked> " . esc_html($role_name) . "</label><br>";
        }
        echo "</fieldset>";
        echo "<p class='description'>Οι χρήστες με αυτούς τους ρόλους δεν θα διαγραφούν ποτέ</p>";
    }

    public static function sanitize_timeout($value) {
        $value = absint($value);
        return ($value >= 1 && $value <= 43200) ? $value : 60;
    }

    public static function sanitize_boolean($value) {
        return (bool) $value;
    }

    public static function sanitize_roles($value) {
        if (!is_array($value)) {
            return ['administrator'];
        }

        $valid_roles = array_keys(wp_roles()->get_names());
        $sanitized = array_intersect($value, $valid_roles);

        // Πάντα να περιλαμβάνουμε τον administrator
        if (!in_array('administrator', $sanitized)) {
            $sanitized[] = 'administrator';
        }

        return $sanitized;
    }
}
WSL_Settings::init();
