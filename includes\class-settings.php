<?php
class WSL_Settings {
    public static function init() {
        add_action('admin_init', [self::class, 'register_settings']);
    }

    public static function register_settings() {


        register_setting('wsl_settings_group', 'wsl_notify_admin', [
            'type' => 'boolean',
            'sanitize_callback' => [self::class, 'sanitize_boolean'],
            'default' => false
        ]);

        register_setting('wsl_settings_group', 'wsl_exclude_roles', [
            'type' => 'array',
            'sanitize_callback' => [self::class, 'sanitize_roles'],
            'default' => ['administrator']
        ]);

        register_setting('wsl_settings_group', 'wsl_email_template', [
            'type' => 'string',
            'sanitize_callback' => [self::class, 'sanitize_email_template'],
            'default' => self::get_default_email_template()
        ]);

        register_setting('wsl_settings_group', 'wsl_email_subject', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'WSL Plugin: Αφαίρεση Ανενεργών Χρηστών'
        ]);

        register_setting('wsl_settings_group', 'wsl_check_frequency', [
            'type' => 'string',
            'sanitize_callback' => [self::class, 'sanitize_frequency'],
            'default' => 'hourly'
        ]);

        register_setting('wsl_settings_group', 'wsl_dry_run', [
            'type' => 'boolean',
            'sanitize_callback' => [self::class, 'sanitize_boolean'],
            'default' => false
        ]);

        register_setting('wsl_settings_group', 'wsl_min_posts_protection', [
            'type' => 'integer',
            'sanitize_callback' => [self::class, 'sanitize_min_posts'],
            'default' => 0
        ]);

        register_setting('wsl_settings_group', 'wsl_enable_checks', [
            'type' => 'boolean',
            'sanitize_callback' => [self::class, 'sanitize_boolean'],
            'default' => true
        ]);

        add_settings_section(
            'wsl_main_section',
            'Κύριες Ρυθμίσεις',
            [self::class, 'main_section_callback'],
            'wsl-settings'
        );

        add_settings_field(
            'wsl_enable_checks',
            'Ενεργοποίηση Ελέγχων',
            [self::class, 'enable_checks_field'],
            'wsl-settings',
            'wsl_main_section'
        );



        add_settings_field(
            'wsl_notify_admin',
            'Ειδοποίηση Διαχειριστή',
            [self::class, 'notify_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        add_settings_field(
            'wsl_exclude_roles',
            'Εξαίρεση Ρόλων',
            [self::class, 'exclude_roles_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        add_settings_field(
            'wsl_check_frequency',
            'Συχνότητα Ελέγχου',
            [self::class, 'frequency_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        add_settings_field(
            'wsl_dry_run',
            'Δοκιμαστική Εκτέλεση',
            [self::class, 'dry_run_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        add_settings_field(
            'wsl_min_posts_protection',
            'Προστασία Χρηστών με Posts',
            [self::class, 'min_posts_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        // Email Section
        add_settings_section(
            'wsl_email_section',
            'Ρυθμίσεις Email',
            [self::class, 'email_section_callback'],
            'wsl-settings'
        );

        add_settings_field(
            'wsl_email_subject',
            'Θέμα Email',
            [self::class, 'email_subject_field'],
            'wsl-settings',
            'wsl_email_section'
        );

        add_settings_field(
            'wsl_email_template',
            'Πρότυπο Email',
            [self::class, 'email_template_field'],
            'wsl-settings',
            'wsl_email_section'
        );
    }

    public static function main_section_callback() {
        echo '<p>Ρυθμίστε τις παραμέτρους για την αυτόματη αφαίρεση ανενεργών χρηστών.</p>';
        echo '<div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">';
        echo '<strong>Πώς λειτουργεί:</strong> Οι χρήστες που εγγράφονται αλλά δεν συνδέονται ποτέ θα διαγράφονται αυτόματα ';
        echo 'μετά από ένα διάστημα που ισούται με τη συχνότητα ελέγχου που έχετε ορίσει. ';
        echo 'Π.χ. αν επιλέξετε "Κάθε 2 ώρες", οι χρήστες θα διαγράφονται αν δεν συνδεθούν εντός 2 ωρών από την εγγραφή τους.';
        echo '</div>';
    }

    public static function enable_checks_field() {
        $value = get_option('wsl_enable_checks', true);
        echo "<input type='checkbox' name='wsl_enable_checks' value='1' " . checked(1, $value, false) . " />";
        echo "<label for='wsl_enable_checks'>Ενεργοποίηση αυτόματων ελέγχων για ανενεργούς χρήστες</label>";
        echo "<p class='description'>Αν απενεργοποιηθεί, δεν θα εκτελούνται αυτόματοι έλεγχοι</p>";
    }

    public static function email_section_callback() {
        echo '<p>Προσαρμόστε τις ειδοποιήσεις email που στέλνονται όταν διαγράφονται χρήστες.</p>';
    }



    public static function notify_field() {
        $value = get_option('wsl_notify_admin', false);
        echo "<input type='checkbox' name='wsl_notify_admin' value='1' " . checked(1, $value, false) . " />";
        echo "<label for='wsl_notify_admin'>Αποστολή email στον διαχειριστή όταν διαγράφονται χρήστες</label>";
    }

    public static function exclude_roles_field() {
        $selected_roles = get_option('wsl_exclude_roles', ['administrator']);
        $all_roles = wp_roles()->get_names();

        echo "<fieldset>";
        foreach ($all_roles as $role_key => $role_name) {
            $checked = in_array($role_key, $selected_roles) ? 'checked' : '';
            echo "<label><input type='checkbox' name='wsl_exclude_roles[]' value='" . esc_attr($role_key) . "' $checked> " . esc_html($role_name) . "</label><br>";
        }
        echo "</fieldset>";
        echo "<p class='description'>Οι χρήστες με αυτούς τους ρόλους δεν θα διαγραφούν ποτέ</p>";
    }



    public static function sanitize_boolean($value) {
        return (bool) $value;
    }

    public static function sanitize_roles($value) {
        if (!is_array($value)) {
            return ['administrator'];
        }

        $valid_roles = array_keys(wp_roles()->get_names());
        $sanitized = array_intersect($value, $valid_roles);

        // Πάντα να περιλαμβάνουμε τον administrator
        if (!in_array('administrator', $sanitized)) {
            $sanitized[] = 'administrator';
        }

        return $sanitized;
    }

    public static function frequency_field() {
        $value = get_option('wsl_check_frequency', 'hourly');
        $frequencies = [
            'hourly' => 'Κάθε ώρα',
            'every_2_hours' => 'Κάθε 2 ώρες',
            'every_4_hours' => 'Κάθε 4 ώρες',
            'twicedaily' => 'Δύο φορές την ημέρα',
            'daily' => 'Καθημερινά',
            'weekly' => 'Εβδομαδιαία'
        ];

        echo "<select name='wsl_check_frequency'>";
        foreach ($frequencies as $key => $label) {
            $selected = selected($value, $key, false);
            echo "<option value='$key' $selected>$label</option>";
        }
        echo "</select>";
        echo "<p class='description'>Πόσο συχνά θα εκτελείται ο έλεγχος και θα διαγράφονται οι ανενεργοί χρήστες που δεν έχουν συνδεθεί ποτέ</p>";
    }

    public static function dry_run_field() {
        $value = get_option('wsl_dry_run', false);
        echo "<input type='checkbox' name='wsl_dry_run' value='1' " . checked(1, $value, false) . " />";
        echo "<label for='wsl_dry_run'>Δοκιμαστική εκτέλεση (δεν διαγράφει χρήστες, μόνο καταγράφει)</label>";
    }

    public static function min_posts_field() {
        $value = get_option('wsl_min_posts_protection', 0);
        echo "<input type='number' name='wsl_min_posts_protection' value='" . esc_attr($value) . "' min='0' />";
        echo "<p class='description'>Χρήστες με περισσότερα από αυτά τα posts δεν θα διαγραφούν (0 = απενεργοποιημένο)</p>";
    }

    public static function email_subject_field() {
        $value = get_option('wsl_email_subject', 'WSL Plugin: Αφαίρεση Ανενεργών Χρηστών');
        echo "<input type='text' name='wsl_email_subject' value='" . esc_attr($value) . "' class='regular-text' />";
    }

    public static function email_template_field() {
        $value = get_option('wsl_email_template', self::get_default_email_template());
        echo "<textarea name='wsl_email_template' rows='10' cols='50' class='large-text'>" . esc_textarea($value) . "</textarea>";
        echo "<p class='description'>Διαθέσιμες μεταβλητές: {count}, {users_list}, {site_name}, {date}</p>";
    }

    public static function get_default_email_template() {
        return "Γεια σας,

Το plugin WSL Remove Inactive Users διέγραψε {count} ανενεργούς χρήστες από το {site_name}.

Διαγραμμένοι χρήστες:
{users_list}

Ημερομηνία: {date}

Αυτό είναι ένα αυτόματο μήνυμα.";
    }

    public static function sanitize_frequency($value) {
        $valid = ['hourly', 'every_2_hours', 'every_4_hours', 'twicedaily', 'daily', 'weekly'];
        return in_array($value, $valid) ? $value : 'hourly';
    }

    public static function sanitize_min_posts($value) {
        return max(0, absint($value));
    }

    public static function sanitize_email_template($value) {
        return wp_kses_post($value);
    }
}
WSL_Settings::init();
