<?php
class WSL_Settings {
    public static function init() {
        add_action('admin_init', [self::class, 'register_settings']);
    }

    public static function register_settings() {
        register_setting('wsl_settings_group', 'wsl_timeout_minutes');
        register_setting('wsl_settings_group', 'wsl_notify_admin');

        add_settings_section('wsl_main_section', 'Main Settings', null, 'wsl-settings');

        add_settings_field(
            'wsl_timeout_minutes',
            'Timeout (minutes)',
            [self::class, 'timeout_field'],
            'wsl-settings',
            'wsl_main_section'
        );

        add_settings_field(
            'wsl_notify_admin',
            'Admin Notification',
            [self::class, 'notify_field'],
            'wsl-settings',
            'wsl_main_section'
        );
    }

    public static function timeout_field() {
        $value = get_option('wsl_timeout_minutes', 60);
        echo "<input type='number' name='wsl_timeout_minutes' value='" . esc_attr($value) . "' min='1' />";
    }

    public static function notify_field() {
        $value = get_option('wsl_notify_admin', false);
        echo "<input type='checkbox' name='wsl_notify_admin' value='1' " . checked(1, $value, false) . " />";
    }
}
WSL_Settings::init();
