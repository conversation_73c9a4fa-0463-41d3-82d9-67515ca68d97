<?php
class WSL_Cleaner {
    public static function remove_inactive_users() {
        $users = get_users(['fields' => ['ID', 'user_registered']]);
        $now = current_time('timestamp');
        $timeout = get_option('wsl_timeout_minutes', 60) * 60;
        $admin_email = get_option('admin_email');
        $notify = get_option('wsl_notify_admin', false);
        $deleted_users = [];

        foreach ($users as $user) {
            $last_login = get_user_meta($user->ID, 'last_login', true);
            $reg_time = strtotime($user->user_registered);
            if (empty($last_login) && ($now - $reg_time > $timeout)) {
                WSL_Logger::log_removal($user->ID);
                require_once(ABSPATH . 'wp-admin/includes/user.php');
                wp_delete_user($user->ID);
                $deleted_users[] = $user->ID;
            }
        }

        if ($notify && !empty($deleted_users)) {
            wp_mail($admin_email, 'WSL Plugin: Users Removed', 'The following user IDs were removed: ' . implode(', ', $deleted_users));
        }
    }
}
