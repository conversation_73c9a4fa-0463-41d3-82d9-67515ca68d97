<?php
class WSL_Cleaner {
    public static function remove_inactive_users() {
        // Έλεγχος αν είναι ενεργοποιημένοι οι έλεγχοι
        if (!get_option('wsl_enable_checks', true)) {
            // Καταγραφή ότι οι έλεγχοι είναι απενεργοποιημένοι
            update_option('wsl_last_run_time', current_time('timestamp'));
            update_option('wsl_last_run_deleted', 0);
            return 0;
        }

        // Λήψη ρόλων που εξαιρούνται από τις ρυθμίσεις
        $excluded_roles = get_option('wsl_exclude_roles', ['administrator']);

        // Βελτιωμένο query για καλύτερη απόδοση - εξαιρούμε τους καθορισμένους ρόλους
        $users = get_users([
            'fields' => ['ID', 'user_registered', 'user_login', 'user_email', 'display_name'],
            'role__not_in' => $excluded_roles,
            'meta_query' => [
                [
                    'key' => 'last_login',
                    'compare' => 'NOT EXISTS'
                ]
            ]
        ]);

        $now = current_time('timestamp');

        // Χρήση της συχνότητας ελέγχου ως timeout
        $frequency = get_option('wsl_check_frequency', 'hourly');
        $frequency_intervals = [
            'hourly' => 3600,        // 1 ώρα
            'every_2_hours' => 7200, // 2 ώρες
            'every_4_hours' => 14400,// 4 ώρες
            'twicedaily' => 43200,   // 12 ώρες
            'daily' => 86400,        // 24 ώρες
            'weekly' => 604800       // 7 ημέρες
        ];
        $timeout = isset($frequency_intervals[$frequency]) ? $frequency_intervals[$frequency] : 3600;

        $admin_email = get_option('admin_email');
        $notify = get_option('wsl_notify_admin', false);
        $dry_run = get_option('wsl_dry_run', false);
        $min_posts_protection = absint(get_option('wsl_min_posts_protection', 0));
        $email_subject = get_option('wsl_email_subject', 'WSL Plugin: Αφαίρεση Ανενεργών Χρηστών');
        $email_template = get_option('wsl_email_template', WSL_Settings::get_default_email_template());
        $deleted_users = [];

        foreach ($users as $user) {
            // Διπλός έλεγχος για ασφάλεια - μην διαγράψουμε ποτέ admin
            if (user_can($user->ID, 'manage_options')) {
                continue;
            }

            $last_login = get_user_meta($user->ID, 'last_login', true);
            $reg_time = strtotime($user->user_registered);

            // Έλεγχος αν ο χρήστης δεν έχει συνδεθεί ποτέ και έχει περάσει το timeout
            if (empty($last_login) && ($now - $reg_time > $timeout)) {
                // Έλεγχος προστασίας posts
                if ($min_posts_protection > 0) {
                    $post_count = count_user_posts($user->ID);
                    if ($post_count >= $min_posts_protection) {
                        continue; // Παράλειψη χρήστη με πολλά posts
                    }
                }

                // Συλλογή πληροφοριών για το log πριν τη διαγραφή
                $user_data = get_userdata($user->ID);
                $user_roles = $user_data->roles;

                WSL_Logger::log_removal($user->ID, $user->user_login, $user->display_name, $user->user_email, $user_roles, $user->user_registered);

                if (!$dry_run) {
                    require_once(ABSPATH . 'wp-admin/includes/user.php');
                    wp_delete_user($user->ID);
                }

                $deleted_users[] = [
                    'id' => $user->ID,
                    'username' => $user->user_login,
                    'email' => $user->user_email,
                    'display_name' => $user->display_name
                ];
            }
        }

        if ($notify && !empty($deleted_users)) {
            // Δημιουργία λίστας χρηστών
            $users_list = '';
            foreach ($deleted_users as $user) {
                $users_list .= "• ID: {$user['id']}, Username: {$user['username']}, Όνομα: {$user['display_name']}, Email: {$user['email']}\n";
            }

            // Αντικατάσταση μεταβλητών στο template
            $message = str_replace([
                '{count}',
                '{users_list}',
                '{site_name}',
                '{date}'
            ], [
                count($deleted_users),
                $users_list,
                get_bloginfo('name'),
                current_time('d/m/Y H:i:s')
            ], $email_template);

            if ($dry_run) {
                $email_subject = '[ΔΟΚΙΜΗ] ' . $email_subject;
                $message = "ΔΟΚΙΜΑΣΤΙΚΗ ΕΚΤΕΛΕΣΗ - Δεν διαγράφηκαν χρήστες\n\n" . $message;
            }

            wp_mail($admin_email, $email_subject, $message);
        }

        // Καταγραφή τελευταίας εκτέλεσης
        update_option('wsl_last_run_time', current_time('timestamp'));
        update_option('wsl_last_run_deleted', count($deleted_users));

        return count($deleted_users);
    }

    /**
     * Ελέγχει αν ένας χρήστης είναι ασφαλής για διαγραφή
     */
    public static function is_user_safe_to_delete($user_id) {
        // Δεν διαγράφουμε ποτέ admins ή super admins
        if (user_can($user_id, 'manage_options')) {
            return false;
        }

        // Δεν διαγράφουμε χρήστες με posts
        $post_count = count_user_posts($user_id);
        if ($post_count > 0) {
            return false;
        }

        return true;
    }
}
