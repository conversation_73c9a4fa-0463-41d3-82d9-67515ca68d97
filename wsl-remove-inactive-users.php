<?php
/**
 * Plugin Name: WSL Remove Inactive Users
 * Description: Removes users who register but never log in within 1 hour.
 * Version: 1.0
 * Author: WSL TakisM
 * License: GPL2
 */

defined('ABSPATH') or die('No script kiddies please!');

require_once plugin_dir_path(__FILE__) . 'includes/class-scheduler.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-cleaner.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-logger.php';
require_once plugin_dir_path(__FILE__) . 'includes/admin-ui.php';

// Activation and deactivation hooks
register_activation_hook(__FILE__, ['WSL_Scheduler', 'activate']);
register_deactivation_hook(__FILE__, ['WSL_Scheduler', 'deactivate']);
require_once plugin_dir_path(__FILE__) . 'includes/class-settings.php';