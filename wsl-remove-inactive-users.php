<?php
/**
 * Plugin Name: WSL Remove Inactive Users
 * Description: Αφαιρεί χρήστες που εγγράφονται αλλά δεν συνδέονται ποτέ εντός καθορισμένου χρόνου. Βελτιωμένη έκδοση με ασφάλεια και καλύτερο UI.
 * Version: 2.0
 * Author: WSL TakisM
 * License: GPL2
 * Text Domain: wsl-remove-inactive-users
 */

defined('ABSPATH') or die('No script kiddies please!');

// Φόρτωση των κλάσεων
require_once plugin_dir_path(__FILE__) . 'includes/class-scheduler.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-cleaner.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-logger.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-settings.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-export.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-statistics.php';
require_once plugin_dir_path(__FILE__) . 'includes/admin-ui.php';

// Hooks ενεργοποίησης και απενεργοποίησης
register_activation_hook(__FILE__, ['WSL_Scheduler', 'activate']);
register_deactivation_hook(__FILE__, ['WSL_Scheduler', 'deactivate']);

// Hook για καταγραφή τελευταίας σύνδεσης
add_action('wp_login', function($user_login, $user) {
    update_user_meta($user->ID, 'last_login', current_time('mysql'));
}, 10, 2);

// Hook για ενημέρωση του schedule όταν αλλάζει η συχνότητα
add_action('update_option_wsl_check_frequency', function($old_value, $new_value) {
    if ($old_value !== $new_value) {
        WSL_Scheduler::update_schedule();
    }
}, 10, 2);