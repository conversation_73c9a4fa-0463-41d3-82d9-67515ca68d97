<?php
class WSL_Scheduler {
    public static function activate() {
        if (!wp_next_scheduled('wsl_check_inactive_users')) {
            wp_schedule_event(time(), 'hourly', 'wsl_check_inactive_users');
        }
    }

    public static function deactivate() {
        wp_clear_scheduled_hook('wsl_check_inactive_users');
    }
}

add_action('wsl_check_inactive_users', 'wsl_execute_user_check');

function wsl_execute_user_check() {
    WSL_Cleaner::remove_inactive_users();
}
