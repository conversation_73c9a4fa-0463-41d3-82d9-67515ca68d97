<?php
class WSL_Scheduler {
    public static function activate() {
        $frequency = get_option('wsl_check_frequency', 'hourly');
        if (!wp_next_scheduled('wsl_check_inactive_users')) {
            wp_schedule_event(time(), $frequency, 'wsl_check_inactive_users');
        }
    }

    public static function deactivate() {
        wp_clear_scheduled_hook('wsl_check_inactive_users');
    }

    public static function update_schedule() {
        // Καθαρισμός παλιού προγραμματισμού
        wp_clear_scheduled_hook('wsl_check_inactive_users');

        // Δημιουργία νέου με την ενημερωμένη συχνότητα
        $frequency = get_option('wsl_check_frequency', 'hourly');
        wp_schedule_event(time(), $frequency, 'wsl_check_inactive_users');
    }

    public static function get_next_run_info() {
        $next_run = wp_next_scheduled('wsl_check_inactive_users');
        $frequency = get_option('wsl_check_frequency', 'hourly');

        if (!$next_run) {
            return [
                'next_run' => false,
                'frequency' => $frequency,
                'interval_seconds' => 0
            ];
        }

        // Υπολογισμός διαστήματος σε δευτερόλεπτα
        $intervals = [
            'hourly' => 3600,
            'twicedaily' => 43200, // 12 ώρες
            'daily' => 86400,
            'weekly' => 604800
        ];

        $interval_seconds = isset($intervals[$frequency]) ? $intervals[$frequency] : 3600;

        return [
            'next_run' => $next_run,
            'frequency' => $frequency,
            'interval_seconds' => $interval_seconds
        ];
    }
}

add_action('wsl_check_inactive_users', 'wsl_execute_user_check');

function wsl_execute_user_check() {
    WSL_Cleaner::remove_inactive_users();
}
