<?php
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'WSL Remove Inactive Users',
        'WSL Remove Inactive Users',
        'manage_options',
        'wsl-remove-inactive-users',
        'wsl_admin_page'
    );
});

function wsl_admin_page() {
    $logs = WSL_Logger::get_logs();
    ?>
    <div class="wrap">
        <h1>WSL Removed Users Log</h1>
        <table class="widefat">
            <thead><tr><th>User ID</th><th>Timestamp</th></tr></thead>
            <tbody>
                <?php foreach ($logs as $log): ?>
                <tr>
                    <td><?php echo esc_html($log['user_id']); ?></td>
                    <td><?php echo esc_html($log['timestamp']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <h2>Settings</h2>
        <form method="post" action="options.php">
            <?php
            settings_fields('wsl_settings_group');
            do_settings_sections('wsl-settings');
            submit_button();
            ?>
        </form>
    </div>
    <?php
}
