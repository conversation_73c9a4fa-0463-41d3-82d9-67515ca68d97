<?php
add_action('admin_menu', function() {
    add_submenu_page(
        'users.php',
        'WSL Ανενεργοί Χρήστες',
        'WSL Ανενεργοί Χρήστες',
        'manage_options',
        'wsl-remove-inactive-users',
        'wsl_admin_page'
    );
});

// Προσθήκη CSS και JS για τα tabs
add_action('admin_head', function() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'users_page_wsl-remove-inactive-users') {
        // Φόρτωση Chart.js
        wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', [], '3.9.1', true);
        ?>
        <style>
        .nav-tab-wrapper { margin-bottom: 20px; }
        .wsl-tab-content { display: none; }
        .wsl-tab-content.active { display: block; }
        .wsl-stats { background: #f1f1f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .wsl-stats .stat-item { display: inline-block; margin-right: 30px; }
        .wsl-stats .stat-number { font-size: 24px; font-weight: bold; color: #0073aa; }
        .wsl-clear-logs { color: #a00; text-decoration: none; }
        .wsl-clear-logs:hover { color: #dc3232; }
        .wsl-countdown {
            background: #e7f3ff;
            border: 1px solid #0073aa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .wsl-countdown-timer {
            font-size: 18px;
            font-weight: bold;
            color: #0073aa;
            letter-spacing: 1px;
        }
        .wsl-countdown-label {
            font-size: 14px;
            margin-bottom: 5px;
            color: #333;
        }
        .wsl-old-log {
            background-color: #fff3cd;
            color: #856404;
        }
        .wsl-filters {
            background: #f9f9f9;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .wsl-filter-row {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            align-items: center;
        }
        .wsl-filter-row label {
            min-width: 100px;
            font-weight: bold;
        }
        .wsl-export-buttons {
            margin: 10px 0;
        }
        .wsl-export-buttons a {
            margin-right: 10px;
        }
        .wsl-chart-container {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .wsl-chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .wsl-summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .wsl-summary-stat {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #0073aa;
        }
        .wsl-summary-stat .number {
            font-size: 24px;
            font-weight: bold;
            color: #0073aa;
        }
        .wsl-summary-stat .label {
            font-size: 14px;
            color: #666;
        }
        </style>
        <script>
        jQuery(document).ready(function($) {
            $('.nav-tab').click(function(e) {
                e.preventDefault();
                $('.nav-tab').removeClass('nav-tab-active');
                $('.wsl-tab-content').removeClass('active');
                $(this).addClass('nav-tab-active');
                $($(this).attr('href')).addClass('active');
            });

            $('.wsl-clear-logs').click(function(e) {
                if (!confirm('Είστε σίγουροι ότι θέλετε να διαγράψετε όλα τα logs;')) {
                    e.preventDefault();
                }
            });

            // Countdown Timer
            function updateCountdown() {
                var nextRun = $('#wsl-next-run-timestamp').data('timestamp');
                if (!nextRun) return;

                var now = Math.floor(Date.now() / 1000);
                var timeLeft = nextRun - now;

                if (timeLeft <= 0) {
                    $('#wsl-countdown-timer').html('00:00:00:000');
                    $('#wsl-countdown-label').html('Ο έλεγχος εκτελείται τώρα...');
                    return;
                }

                var hours = Math.floor(timeLeft / 3600);
                var minutes = Math.floor((timeLeft % 3600) / 60);
                var seconds = timeLeft % 60;
                var milliseconds = Math.floor((Date.now() % 1000));

                var timeString =
                    String(hours).padStart(2, '0') + ':' +
                    String(minutes).padStart(2, '0') + ':' +
                    String(seconds).padStart(2, '0') + ':' +
                    String(milliseconds).padStart(3, '0');

                $('#wsl-countdown-timer').html(timeString);
            }

            // Ενημέρωση κάθε 100ms για ακρίβεια
            if ($('#wsl-countdown-timer').length) {
                updateCountdown();
                setInterval(updateCountdown, 100);
            }

            // Φίλτρα logs
            $('#wsl-filter-form').on('submit', function(e) {
                e.preventDefault();
                filterLogs();
            });

            $('#wsl-search-input').on('input', function() {
                var searchTerm = $(this).val();
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    searchLogs(searchTerm);
                }
            });

            $('#wsl-reset-filters').on('click', function() {
                $('#wsl-filter-form')[0].reset();
                $('#wsl-search-input').val('');
                $('.wsl-log-row').show();
            });

            function filterLogs() {
                var filters = {
                    date_from: $('#filter-date-from').val(),
                    date_to: $('#filter-date-to').val(),
                    username: $('#filter-username').val(),
                    email: $('#filter-email').val(),
                    role: $('#filter-role').val(),
                    user_id: $('#filter-user-id').val()
                };

                $('.wsl-log-row').each(function() {
                    var row = $(this);
                    var show = true;

                    // Έλεγχος κάθε φίλτρου
                    if (filters.date_from && row.data('date') < filters.date_from) show = false;
                    if (filters.date_to && row.data('date') > filters.date_to) show = false;
                    if (filters.username && row.data('username').toLowerCase().indexOf(filters.username.toLowerCase()) === -1) show = false;
                    if (filters.email && row.data('email').toLowerCase().indexOf(filters.email.toLowerCase()) === -1) show = false;
                    if (filters.role && row.data('role').toLowerCase().indexOf(filters.role.toLowerCase()) === -1) show = false;
                    if (filters.user_id && row.data('user-id') != filters.user_id) show = false;

                    row.toggle(show);
                });
            }

            function searchLogs(searchTerm) {
                if (!searchTerm) {
                    $('.wsl-log-row').show();
                    return;
                }

                $('.wsl-log-row').each(function() {
                    var row = $(this);
                    var text = row.text().toLowerCase();
                    row.toggle(text.indexOf(searchTerm.toLowerCase()) !== -1);
                });
            }
        });
        </script>
        <?php
    }
});

function wsl_admin_page() {
    // Χειρισμός actions
    if (isset($_GET['action']) && $_GET['action'] === 'clear_logs' && wp_verify_nonce($_GET['_wpnonce'], 'wsl_clear_logs')) {
        WSL_Logger::clear_logs();
        echo '<div class="notice notice-success"><p>Τα logs διαγράφηκαν επιτυχώς!</p></div>';
    }

    // Χειρισμός φίλτρων
    $filters = [];
    if (isset($_GET['filter_submit'])) {
        $filters = [
            'date_from' => sanitize_text_field($_GET['date_from'] ?? ''),
            'date_to' => sanitize_text_field($_GET['date_to'] ?? ''),
            'username' => sanitize_text_field($_GET['username'] ?? ''),
            'email' => sanitize_text_field($_GET['email'] ?? ''),
            'role' => sanitize_text_field($_GET['role'] ?? ''),
            'user_id' => sanitize_text_field($_GET['user_id'] ?? ''),
        ];
        $logs = WSL_Logger::get_filtered_logs($filters);
    } else {
        $logs = WSL_Logger::get_logs();
    }

    $logs_count = WSL_Logger::get_logs_count();
    $current_tab = isset($_GET['tab']) ? $_GET['tab'] : 'logs';
    $export_urls = WSL_Export::get_export_buttons();
    $stats = WSL_Statistics::get_chart_data();
    ?>
    <div class="wrap">
        <h1>WSL Διαχείριση Ανενεργών Χρηστών</h1>

        <h2 class="nav-tab-wrapper">
            <a href="#logs" class="nav-tab <?php echo $current_tab === 'logs' ? 'nav-tab-active' : ''; ?>">
                📋 Αρχείο Διαγραφών (<?php echo $logs_count; ?>)
            </a>
            <a href="#statistics" class="nav-tab <?php echo $current_tab === 'statistics' ? 'nav-tab-active' : ''; ?>">
                📈 Στατιστικά
            </a>
            <a href="#settings" class="nav-tab <?php echo $current_tab === 'settings' ? 'nav-tab-active' : ''; ?>">
                ⚙️ Ρυθμίσεις
            </a>
        </h2>

        <!-- Tab: Logs -->
        <div id="logs" class="wsl-tab-content <?php echo $current_tab === 'logs' ? 'active' : ''; ?>">
            <!-- Φίλτρα Αναζήτησης -->
            <div class="wsl-filters">
                <h3>🔍 Φίλτρα & Αναζήτηση</h3>

                <div class="wsl-filter-row">
                    <label>Γρήγορη Αναζήτηση:</label>
                    <input type="text" id="wsl-search-input" placeholder="Αναζήτηση σε όλα τα πεδία..." class="regular-text">
                    <button type="button" id="wsl-reset-filters" class="button">Καθαρισμός</button>
                </div>

                <form id="wsl-filter-form" method="get">
                    <input type="hidden" name="page" value="wsl-remove-inactive-users">
                    <input type="hidden" name="filter_submit" value="1">

                    <div class="wsl-filter-row">
                        <label>Από Ημερομηνία:</label>
                        <input type="date" name="date_from" id="filter-date-from" value="<?php echo esc_attr($filters['date_from'] ?? ''); ?>">

                        <label>Έως Ημερομηνία:</label>
                        <input type="date" name="date_to" id="filter-date-to" value="<?php echo esc_attr($filters['date_to'] ?? ''); ?>">
                    </div>

                    <div class="wsl-filter-row">
                        <label>Username:</label>
                        <input type="text" name="username" id="filter-username" value="<?php echo esc_attr($filters['username'] ?? ''); ?>" class="regular-text">

                        <label>Email:</label>
                        <input type="text" name="email" id="filter-email" value="<?php echo esc_attr($filters['email'] ?? ''); ?>" class="regular-text">
                    </div>

                    <div class="wsl-filter-row">
                        <label>Ρόλος:</label>
                        <input type="text" name="role" id="filter-role" value="<?php echo esc_attr($filters['role'] ?? ''); ?>" class="regular-text">

                        <label>User ID:</label>
                        <input type="number" name="user_id" id="filter-user-id" value="<?php echo esc_attr($filters['user_id'] ?? ''); ?>">

                        <button type="submit" class="button button-primary">Εφαρμογή Φίλτρων</button>
                    </div>
                </form>
            </div>

            <!-- Export Buttons -->
            <div class="wsl-export-buttons">
                <h3>📊 Export Δεδομένων</h3>
                <a href="<?php echo esc_url($export_urls['csv']); ?>" class="button">📄 Export CSV</a>
                <a href="<?php echo esc_url($export_urls['excel']); ?>" class="button">📊 Export Excel</a>
            </div>

            <!-- Στατιστικά Σύνοψης -->
            <div class="wsl-stats">
                <div class="stat-item">
                    <div class="stat-number"><?php echo $logs_count; ?></div>
                    <div>Συνολικές Διαγραφές</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($logs); ?></div>
                    <div>Εμφανιζόμενες</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo get_option('wsl_timeout_minutes', 60); ?></div>
                    <div>Λεπτά Timeout</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo wp_next_scheduled('wsl_check_inactive_users') ? 'Ενεργό' : 'Ανενεργό'; ?></div>
                    <div>Αυτόματος Έλεγχος</div>
                </div>
            </div>

            <?php if (!empty($logs)): ?>
                <div style="margin-bottom: 10px;">
                    <a href="<?php echo wp_nonce_url(admin_url('users.php?page=wsl-remove-inactive-users&action=clear_logs'), 'wsl_clear_logs'); ?>"
                       class="wsl-clear-logs">Διαγραφή όλων των logs</a>
                </div>

                <table class="widefat fixed striped">
                    <thead>
                        <tr>
                            <th style="width: 60px;">User ID</th>
                            <th style="width: 120px;">Username</th>
                            <th style="width: 150px;">Όνομα</th>
                            <th style="width: 200px;">Email</th>
                            <th style="width: 100px;">Ρόλος</th>
                            <th style="width: 130px;">Ημ/νία Διαγραφής</th>
                            <th style="width: 130px;">Τελευταία Σύνδεση</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log):
                            // Έλεγχος αν είναι παλιό log (χωρίς τις νέες πληροφορίες)
                            $is_old_log = !isset($log['username']) || empty($log['username']);
                            $row_class = $is_old_log ? 'wsl-old-log' : '';
                            $log_date = date('Y-m-d', strtotime($log['timestamp']));
                        ?>
                        <tr class="wsl-log-row <?php echo $row_class; ?>"
                            data-user-id="<?php echo esc_attr($log['user_id']); ?>"
                            data-username="<?php echo esc_attr($log['username'] ?? ''); ?>"
                            data-email="<?php echo esc_attr($log['email'] ?? ''); ?>"
                            data-role="<?php echo esc_attr($log['roles'] ?? ''); ?>"
                            data-date="<?php echo esc_attr($log_date); ?>">
                            <td><?php echo esc_html($log['user_id']); ?></td>
                            <td>
                                <?php
                                if (isset($log['username']) && !empty($log['username'])) {
                                    echo esc_html($log['username']);
                                } else {
                                    echo '<em>Μη διαθέσιμο</em>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if (isset($log['display_name']) && !empty($log['display_name'])) {
                                    echo esc_html($log['display_name']);
                                } else {
                                    echo '<em>Μη διαθέσιμο</em>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if (isset($log['email']) && !empty($log['email'])) {
                                    echo esc_html($log['email']);
                                } else {
                                    echo '<em>Μη διαθέσιμο</em>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if (isset($log['roles']) && !empty($log['roles'])) {
                                    echo esc_html($log['roles']);
                                } else {
                                    echo '<em>Μη διαθέσιμο</em>';
                                }
                                ?>
                            </td>
                            <td><?php echo esc_html(mysql2date('d/m/Y H:i', $log['timestamp'])); ?></td>
                            <td>
                                <?php
                                if (isset($log['last_login']) && !empty($log['last_login'])) {
                                    echo esc_html(mysql2date('d/m/Y H:i', $log['last_login']));
                                } else {
                                    echo '<em>Ποτέ</em>';
                                }
                                ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <p class="description">
                    <strong>Σημείωση:</strong> Οι εγγραφές με κίτρινο φόντο είναι παλιά logs που δεν περιέχουν λεπτομερείς πληροφορίες χρήστη.
                    Οι νέες διαγραφές θα περιέχουν όλες τις πληροφορίες.
                </p>
            <?php else: ?>
                <p>Δεν υπάρχουν καταγραφές διαγραφών χρηστών.</p>
            <?php endif; ?>
        </div>

        <!-- Tab: Statistics -->
        <div id="statistics" class="wsl-tab-content <?php echo $current_tab === 'statistics' ? 'active' : ''; ?>">
            <h2>📈 Στατιστικά Διαγραφών</h2>

            <!-- Στατιστικά Σύνοψης -->
            <div class="wsl-summary-stats">
                <?php foreach ($stats['summary'] as $key => $value): ?>
                <div class="wsl-summary-stat">
                    <div class="number">
                        <?php
                        switch($key) {
                            case 'total': echo $value; break;
                            case 'today': echo $value; break;
                            case 'this_week': echo $value; break;
                            case 'this_month': echo $value; break;
                            case 'avg_per_day': echo $value; break;
                            case 'first_deletion': echo $value ? date('d/m/Y', strtotime($value)) : 'Καμία'; break;
                            case 'last_deletion': echo $value ? date('d/m/Y', strtotime($value)) : 'Καμία'; break;
                        }
                        ?>
                    </div>
                    <div class="label">
                        <?php
                        switch($key) {
                            case 'total': echo 'Συνολικές Διαγραφές'; break;
                            case 'today': echo 'Σήμερα'; break;
                            case 'this_week': echo 'Αυτή την Εβδομάδα'; break;
                            case 'this_month': echo 'Αυτόν τον Μήνα'; break;
                            case 'avg_per_day': echo 'Μέσος Όρος/Ημέρα'; break;
                            case 'first_deletion': echo 'Πρώτη Διαγραφή'; break;
                            case 'last_deletion': echo 'Τελευταία Διαγραφή'; break;
                        }
                        ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Γραφήματα -->
            <div class="wsl-chart-grid">
                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Ημέρα (Τελευταίες 30 ημέρες)</h3>
                    <canvas id="dailyChart" width="400" height="200"></canvas>
                </div>

                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Ρόλο</h3>
                    <canvas id="rolesChart" width="400" height="200"></canvas>
                </div>
            </div>

            <div class="wsl-chart-grid">
                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Μήνα (Τελευταίοι 12 μήνες)</h3>
                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                </div>

                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Ώρα</h3>
                    <canvas id="hourlyChart" width="400" height="200"></canvas>
                </div>
            </div>

            <script>
            // Δεδομένα για γραφήματα
            const chartData = <?php echo json_encode($stats); ?>;

            // Γράφημα ημερήσιων διαγραφών
            const dailyCtx = document.getElementById('dailyChart').getContext('2d');
            new Chart(dailyCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(chartData.daily),
                    datasets: [{
                        label: 'Διαγραφές',
                        data: Object.values(chartData.daily),
                        borderColor: '#0073aa',
                        backgroundColor: 'rgba(0, 115, 170, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Γράφημα ρόλων
            const rolesCtx = document.getElementById('rolesChart').getContext('2d');
            new Chart(rolesCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(chartData.roles),
                    datasets: [{
                        data: Object.values(chartData.roles),
                        backgroundColor: [
                            '#0073aa', '#005177', '#00a0d2', '#72aee6',
                            '#2271b1', '#135e96', '#043959', '#1d2327'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Γράφημα μηνιαίων διαγραφών
            const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
            new Chart(monthlyCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(chartData.monthly),
                    datasets: [{
                        label: 'Διαγραφές',
                        data: Object.values(chartData.monthly),
                        backgroundColor: '#0073aa'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Γράφημα ωριαίων διαγραφών
            const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
            new Chart(hourlyCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(chartData.hourly),
                    datasets: [{
                        label: 'Διαγραφές',
                        data: Object.values(chartData.hourly),
                        backgroundColor: '#72aee6'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
            </script>
        </div>

        <!-- Tab: Settings -->
        <div id="settings" class="wsl-tab-content <?php echo $current_tab === 'settings' ? 'active' : ''; ?>">
            <form method="post" action="options.php">
                <?php
                settings_fields('wsl_settings_group');
                do_settings_sections('wsl-settings');
                submit_button('Αποθήκευση Ρυθμίσεων');
                ?>
            </form>

            <hr>

            <h3>Πληροφορίες Plugin</h3>
            <table class="form-table">
                <tr>
                    <th scope="row">Επόμενος Έλεγχος</th>
                    <td>
                        <?php
                        $next_run = wp_next_scheduled('wsl_check_inactive_users');
                        if ($next_run) {
                            echo date('d/m/Y H:i:s', $next_run);
                            echo '<div id="wsl-next-run-timestamp" data-timestamp="' . $next_run . '" style="display:none;"></div>';
                        } else {
                            echo 'Δεν είναι προγραμματισμένος';
                        }
                        ?>
                    </td>
                </tr>
                <?php if ($next_run): ?>
                <tr>
                    <th scope="row">Χρόνος μέχρι τον Έλεγχο</th>
                    <td>
                        <div class="wsl-countdown">
                            <div class="wsl-countdown-label" id="wsl-countdown-label">Υπολειπόμενος χρόνος:</div>
                            <div class="wsl-countdown-timer" id="wsl-countdown-timer">--:--:--:---</div>
                        </div>
                    </td>
                </tr>
                <?php endif; ?>
                <tr>
                    <th scope="row">Συνολικοί Χρήστες</th>
                    <td><?php echo count_users()['total_users']; ?></td>
                </tr>
                <tr>
                    <th scope="row">Έκδοση Plugin</th>
                    <td>2.0 (Pro Edition με Στατιστικά & Export)</td>
                </tr>
            </table>
        </div>
    </div>
    <?php
}
