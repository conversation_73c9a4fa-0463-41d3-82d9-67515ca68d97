<?php
add_action('admin_menu', function() {
    add_submenu_page(
        'users.php',
        'WSL Ανενεργοί Χρήστες',
        'WSL Ανενεργοί Χρήστες',
        'manage_options',
        'wsl-remove-inactive-users',
        'wsl_admin_page'
    );
});

// Προσθήκη CSS για τα tabs
add_action('admin_head', function() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'users_page_wsl-remove-inactive-users') {
        ?>
        <style>
        .nav-tab-wrapper { margin-bottom: 20px; }
        .wsl-tab-content { display: none; }
        .wsl-tab-content.active { display: block; }
        .wsl-stats { background: #f1f1f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .wsl-stats .stat-item { display: inline-block; margin-right: 30px; }
        .wsl-stats .stat-number { font-size: 24px; font-weight: bold; color: #0073aa; }
        .wsl-clear-logs { color: #a00; text-decoration: none; }
        .wsl-clear-logs:hover { color: #dc3232; }
        </style>
        <script>
        jQuery(document).ready(function($) {
            $('.nav-tab').click(function(e) {
                e.preventDefault();
                $('.nav-tab').removeClass('nav-tab-active');
                $('.wsl-tab-content').removeClass('active');
                $(this).addClass('nav-tab-active');
                $($(this).attr('href')).addClass('active');
            });

            $('.wsl-clear-logs').click(function(e) {
                if (!confirm('Είστε σίγουροι ότι θέλετε να διαγράψετε όλα τα logs;')) {
                    e.preventDefault();
                }
            });
        });
        </script>
        <?php
    }
});

function wsl_admin_page() {
    // Χειρισμός actions
    if (isset($_GET['action']) && $_GET['action'] === 'clear_logs' && wp_verify_nonce($_GET['_wpnonce'], 'wsl_clear_logs')) {
        WSL_Logger::clear_logs();
        echo '<div class="notice notice-success"><p>Τα logs διαγράφηκαν επιτυχώς!</p></div>';
    }

    $logs = WSL_Logger::get_logs();
    $logs_count = WSL_Logger::get_logs_count();
    $current_tab = isset($_GET['tab']) ? $_GET['tab'] : 'logs';
    ?>
    <div class="wrap">
        <h1>WSL Διαχείριση Ανενεργών Χρηστών</h1>

        <h2 class="nav-tab-wrapper">
            <a href="#logs" class="nav-tab <?php echo $current_tab === 'logs' ? 'nav-tab-active' : ''; ?>">
                Αρχείο Διαγραφών (<?php echo $logs_count; ?>)
            </a>
            <a href="#settings" class="nav-tab <?php echo $current_tab === 'settings' ? 'nav-tab-active' : ''; ?>">
                Ρυθμίσεις
            </a>
        </h2>

        <!-- Tab: Logs -->
        <div id="logs" class="wsl-tab-content <?php echo $current_tab === 'logs' ? 'active' : ''; ?>">
            <div class="wsl-stats">
                <div class="stat-item">
                    <div class="stat-number"><?php echo $logs_count; ?></div>
                    <div>Συνολικές Διαγραφές</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo get_option('wsl_timeout_minutes', 60); ?></div>
                    <div>Λεπτά Timeout</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo wp_next_scheduled('wsl_check_inactive_users') ? 'Ενεργό' : 'Ανενεργό'; ?></div>
                    <div>Αυτόματος Έλεγχος</div>
                </div>
            </div>

            <?php if (!empty($logs)): ?>
                <div style="margin-bottom: 10px;">
                    <a href="<?php echo wp_nonce_url(admin_url('users.php?page=wsl-remove-inactive-users&action=clear_logs'), 'wsl_clear_logs'); ?>"
                       class="wsl-clear-logs">Διαγραφή όλων των logs</a>
                </div>

                <table class="widefat fixed striped">
                    <thead>
                        <tr>
                            <th style="width: 60px;">User ID</th>
                            <th style="width: 120px;">Username</th>
                            <th style="width: 150px;">Όνομα</th>
                            <th style="width: 200px;">Email</th>
                            <th style="width: 100px;">Ρόλος</th>
                            <th style="width: 130px;">Ημ/νία Διαγραφής</th>
                            <th style="width: 130px;">Τελευταία Σύνδεση</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                        <tr>
                            <td><?php echo esc_html($log['user_id']); ?></td>
                            <td><?php echo esc_html($log['username'] ?? '-'); ?></td>
                            <td><?php echo esc_html($log['display_name'] ?? '-'); ?></td>
                            <td><?php echo esc_html($log['email'] ?? '-'); ?></td>
                            <td><?php echo esc_html($log['roles'] ?? '-'); ?></td>
                            <td><?php echo esc_html(mysql2date('d/m/Y H:i', $log['timestamp'])); ?></td>
                            <td><?php echo esc_html($log['last_login'] ?: 'Ποτέ'); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>Δεν υπάρχουν καταγραφές διαγραφών χρηστών.</p>
            <?php endif; ?>
        </div>

        <!-- Tab: Settings -->
        <div id="settings" class="wsl-tab-content <?php echo $current_tab === 'settings' ? 'active' : ''; ?>">
            <form method="post" action="options.php">
                <?php
                settings_fields('wsl_settings_group');
                do_settings_sections('wsl-settings');
                submit_button('Αποθήκευση Ρυθμίσεων');
                ?>
            </form>

            <hr>

            <h3>Πληροφορίες Plugin</h3>
            <table class="form-table">
                <tr>
                    <th scope="row">Επόμενος Έλεγχος</th>
                    <td>
                        <?php
                        $next_run = wp_next_scheduled('wsl_check_inactive_users');
                        if ($next_run) {
                            echo date('d/m/Y H:i:s', $next_run);
                        } else {
                            echo 'Δεν είναι προγραμματισμένος';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Συνολικοί Χρήστες</th>
                    <td><?php echo count_users()['total_users']; ?></td>
                </tr>
                <tr>
                    <th scope="row">Έκδοση Plugin</th>
                    <td>1.1 (Βελτιωμένη)</td>
                </tr>
            </table>
        </div>
    </div>
    <?php
}
